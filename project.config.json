{"description": "星空方块大师 - 创新俄罗斯方块微信小游戏", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "es6": true, "enhance": false, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": false, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": false, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": false, "useApiHook": false, "useApiHostProcess": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": false, "disableUseStrict": false, "minifyWXML": false, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true}, "compileType": "game", "libVersion": "3.8.11", "appid": "wx84c315c7d26fd63d", "projectname": "星空方块大师", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}